import React, { useState } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'

interface AIFormFillerDialogProps {
  isOpen: boolean
  onClose: () => void
  fillData: {
    project: any
    form: any
    fields: Array<{
      name: string
      label?: string
      placeholder?: string
      type: string
      required?: boolean
    }>
  }
  onFillComplete: (optimizedData: any) => void
}

export function AIFormFillerDialog({ 
  isOpen, 
  onClose, 
  fillData,
  onFillComplete 
}: AIFormFillerDialogProps) {
  const { optimizeContent, aiOptimizing, settings } = useAppStore()

  const [customPrompt, setCustomPrompt] = useState('')
  const [targetPlatform, setTargetPlatform] = useState('')
  const [tone, setTone] = useState<'professional' | 'casual' | 'marketing'>('professional')
  const [optimizedFields, setOptimizedFields] = useState<any>(null)
  const [showResults, setShowResults] = useState(false)

  const platforms = [
    { value: '', label: '通用表单' },
    { value: 'producthunt', label: 'Product Hunt' },
    { value: 'hacker_news', label: 'Hacker News' },
    { value: 'reddit', label: 'Reddit' },
    { value: 'twitter', label: 'Twitter' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'github', label: 'GitHub' },
    { value: 'indie_hackers', label: 'Indie Hackers' },
    { value: 'betalist', label: 'BetaList' },
    { value: 'launching_next', label: 'Launching Next' }
  ]

  const tones = [
    { value: 'professional', label: '专业正式' },
    { value: 'casual', label: '轻松随意' },
    { value: 'marketing', label: '营销推广' }
  ]

  React.useEffect(() => {
    if (isOpen) {
      setOptimizedFields(null)
      setShowResults(false)
      setCustomPrompt('')
    }
  }, [isOpen])

  const handleAIOptimize = async () => {
    if (!fillData) return

    try {
      // 构建AI提示，包含项目信息和表单字段信息
      const prompt = `
请为以下项目优化表单填充内容：

项目信息：
- 名称：${fillData.project.name}
- 域名：${fillData.project.domain}
- 描述：${fillData.project.info?.introduction || ''}
- 分类：${fillData.project.category || ''}

表单字段：
${fillData.fields.map(field => 
  `- ${field.label || field.name || field.placeholder} (${field.type}${field.required ? ', 必填' : ''})`
).join('\n')}

${targetPlatform ? `目标平台：${platforms.find(p => p.value === targetPlatform)?.label}` : ''}
${customPrompt ? `特殊要求：${customPrompt}` : ''}

请为每个字段生成适合的内容，内容要：
1. 符合${tones.find(t => t.value === tone)?.label}的风格
2. 针对目标平台进行优化
3. 准确反映项目特点
4. 适合字段类型和长度限制

请以JSON格式返回，格式如下：
{
  "fieldName1": "优化后的内容1",
  "fieldName2": "优化后的内容2"
}
`

      const optimizedContent = await optimizeContent(prompt, {
        targetPlatform: targetPlatform || undefined,
        fieldType: 'description',
        maxLength: 2000,
        tone,
        customPrompt: '生成JSON格式的表单填充数据'
      })

      // 尝试解析JSON结果
      try {
        const parsed = JSON.parse(optimizedContent)
        setOptimizedFields(parsed)
        setShowResults(true)
      } catch (parseError) {
        // 如果解析失败，尝试提取可能的内容
        console.error('AI返回内容解析失败:', parseError)
        // 这里可以添加更智能的内容解析逻辑
        setOptimizedFields({
          error: 'AI返回的内容格式不正确，请重试',
          rawContent: optimizedContent
        })
        setShowResults(true)
      }
    } catch (error) {
      console.error('AI优化失败:', error)
    }
  }

  const handleAcceptOptimization = () => {
    if (optimizedFields && !optimizedFields.error) {
      onFillComplete(optimizedFields)
    }
    onClose()
  }

  const handleCancel = () => {
    onClose()
  }

  const isAIAvailable = settings.aiOptimizationEnabled

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI 智能填充</DialogTitle>
          <DialogDescription>
            使用AI根据项目信息和表单结构智能生成填充内容
          </DialogDescription>
        </DialogHeader>

        {!isAIAvailable ? (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="py-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="font-medium">AI优化功能不可用</span>
              </div>
              <p className="text-sm text-yellow-700 mt-2">
                请检查网络连接或在设置中配置API密钥以启用AI优化功能
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* 项目和表单概览 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">项目信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div><span className="font-medium">名称:</span> {fillData.project.name}</div>
                  <div><span className="font-medium">域名:</span> {fillData.project.domain}</div>
                  {fillData.project.category && (
                    <div><span className="font-medium">分类:</span> {fillData.project.category}</div>
                  )}
                  {fillData.project.info?.introduction && (
                    <div>
                      <span className="font-medium">描述:</span>
                      <p className="text-muted-foreground mt-1">
                        {fillData.project.info.introduction.length > 150 
                          ? `${fillData.project.info.introduction.substring(0, 150)}...` 
                          : fillData.project.info.introduction}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">表单字段</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1">
                    {fillData.fields.map((field, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span>{field.label || field.name || field.placeholder || '未知字段'}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground">{field.type}</span>
                          {field.required && <span className="text-red-500">*</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 优化配置 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  目标平台
                </label>
                <select
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={targetPlatform}
                  onChange={(e) => setTargetPlatform(e.target.value)}
                >
                  {platforms.map(platform => (
                    <option key={platform.value} value={platform.value}>
                      {platform.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  内容风格
                </label>
                <select
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={tone}
                  onChange={(e) => setTone(e.target.value as any)}
                >
                  {tones.map(toneOption => (
                    <option key={toneOption.value} value={toneOption.value}>
                      {toneOption.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  自定义提示
                </label>
                <Input
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="添加特殊要求（可选）"
                />
              </div>
            </div>

            {/* 优化按钮 */}
            {!showResults && (
              <div className="flex justify-center">
                <Button
                  onClick={handleAIOptimize}
                  disabled={aiOptimizing}
                  size="lg"
                >
                  {aiOptimizing ? (
                    <>
                      <svg className="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      AI分析中...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      开始AI填充
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* 优化结果 */}
            {showResults && optimizedFields && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">AI优化结果</h3>
                
                {optimizedFields.error ? (
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="py-4">
                      <div className="text-red-800">
                        <p className="font-medium">优化失败</p>
                        <p className="text-sm mt-1">{optimizedFields.error}</p>
                        {optimizedFields.rawContent && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-sm">查看原始返回内容</summary>
                            <pre className="mt-2 p-2 bg-white rounded text-xs overflow-auto">
                              {optimizedFields.rawContent}
                            </pre>
                          </details>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-3">
                    {Object.entries(optimizedFields).map(([fieldName, content]: [string, any]) => (
                      <Card key={fieldName}>
                        <CardContent className="py-4">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium">{fieldName}</h4>
                            <span className="text-xs text-muted-foreground">
                              {String(content).length} 字符
                            </span>
                          </div>
                          <div className="p-3 bg-green-50 border border-green-200 rounded text-sm">
                            {String(content)}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={handleCancel}>
                    取消
                  </Button>
                  <Button 
                    onClick={() => setShowResults(false)}
                    variant="outline"
                  >
                    重新优化
                  </Button>
                  {!optimizedFields.error && (
                    <Button onClick={handleAcceptOptimization}>
                      使用此结果填充
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* 没有结果时的操作按钮 */}
            {!showResults && (
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={handleCancel}>
                  取消
                </Button>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}