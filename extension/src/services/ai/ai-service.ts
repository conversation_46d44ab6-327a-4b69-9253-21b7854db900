import type { 
  Project, 
  OptimizationContext, 
  OptimizationRequest,
  OptimizationResponse,
  AIOptimizationResult
} from '~/types'
import type { AIService as IAIService, StorageService } from '../interfaces'
import { StorageFactory } from '../storage'
import { getAppConfig } from '~/lib/config'

interface AIConfig {
  apiUrl: string
  apiKey?: string
  timeout: number
  tier: 'premium' | 'free'
}

export class AIService implements IAIService {
  private config: AIConfig | null = null
  private storageFactory: StorageFactory
  private optimizationId: string | null = null

  constructor() {
    this.storageFactory = StorageFactory.getInstance()
    this.initializeConfig()
  }

  private async initializeConfig(): Promise<void> {
    try {
      const appConfig = getAppConfig()
      const storage = await this.storageFactory.getStorageService()
      
      // 尝试获取用户设置中的API密钥
      let apiKey: string | undefined
      try {
        const settings = await (storage as any).get('settings')
        apiKey = settings?.apiKey
      } catch (error) {
        console.warn('无法获取用户设置:', error)
      }

      // 根据是否有API密钥确定用户层级
      const tier: 'premium' | 'free' = apiKey ? 'premium' : 'free'
      
      this.config = {
        apiUrl: `${appConfig.apiUrl}/extension`,
        apiKey,
        timeout: appConfig.timeout,
        tier
      }

      // 为免费用户生成唯一ID
      if (tier === 'free') {
        this.optimizationId = this.generateOptimizationId()
      }

      console.log(`AI服务已初始化 (${tier} tier)`)
    } catch (error) {
      console.error('AI服务初始化失败:', error)
      
      // 降级为免费配置
      const appConfig = getAppConfig()
      this.config = {
        apiUrl: `${appConfig.apiUrl}/extension`,
        timeout: appConfig.timeout,
        tier: 'free'
      }
      this.optimizationId = this.generateOptimizationId()
    }
  }

  async optimizeContent(content: string, context: OptimizationContext): Promise<string> {
    if (!this.isAvailable()) {
      throw new Error('AI服务不可用')
    }

    if (!content || content.trim().length === 0) {
      throw new Error('内容不能为空')
    }

    try {
      const optimized = await this.callOptimizeAPI(content, context)
      console.log('内容优化成功')
      return optimized
    } catch (error) {
      console.error('内容优化失败:', error)
      
      // 降级处理：返回原始内容
      console.warn('使用原始内容作为降级方案')
      return content
    }
  }

  async generateDescription(project: Project, targetPlatform?: string): Promise<string> {
    const context: OptimizationContext = {
      targetPlatform,
      fieldType: 'description',
      maxLength: 500,
      tone: 'professional'
    }

    // 基于项目信息构建提示内容
    const baseContent = this.buildProjectDescription(project)
    
    try {
      return await this.optimizeContent(baseContent, context)
    } catch (error) {
      console.error('生成项目描述失败:', error)
      return baseContent
    }
  }

  async suggestTags(project: Project): Promise<string[]> {
    try {
      const context: OptimizationContext = {
        fieldType: 'tags',
        tone: 'professional'
      }

      const content = `项目名称: ${project.name}\n项目介绍: ${project.info?.introduction || project.name}\n项目域名: ${project.domain}`
      const response = await this.callOptimizeAPI(content, { ...context, maxLength: 200 })
      
      // 解析标签字符串
      const suggestedTags = response
        .split(/[,，\n]/)
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0 && tag.length < 50)
        .slice(0, 10) // 最多10个标签

      return suggestedTags
    } catch (error) {
      console.error('生成标签建议失败:', error)
      
      // 降级方案：基于项目信息提取关键词
      return this.extractKeywordsFromProject(project)
    }
  }

  isAvailable(): boolean {
    return this.config !== null && navigator.onLine
  }

  generateOptimizationId(): string {
    // 为免费用户生成唯一ID
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    const browserFingerprint = this.getBrowserFingerprint()
    
    return `free_${timestamp}_${random}_${browserFingerprint}`
  }

  // 公共方法：更新API密钥
  async updateApiKey(apiKey?: string): Promise<void> {
    if (!this.config) {
      await this.initializeConfig()
      return
    }

    const tier: 'premium' | 'free' = apiKey ? 'premium' : 'free'
    
    this.config = {
      ...this.config,
      apiKey,
      tier
    }

    // 为免费用户生成或清除优化ID
    if (tier === 'free') {
      this.optimizationId = this.generateOptimizationId()
    } else {
      this.optimizationId = null
    }

    console.log(`AI服务配置已更新 (${tier} tier)`)
  }

  // 获取当前服务层级
  getTier(): 'premium' | 'free' | null {
    return this.config?.tier || null
  }

  // 高级AI功能
  async optimizeForPlatform(project: Project, platform: string): Promise<{
    title: string
    description: string
    tags: string[]
    customFields: Record<string, string>
  }> {
    try {
      const optimizations = await Promise.all([
        this.optimizeContent(project.name, {
          targetPlatform: platform,
          fieldType: 'title',
          maxLength: 100,
          tone: 'marketing'
        }),
        this.optimizeContent(project.info?.introduction || project.name, {
          targetPlatform: platform,
          fieldType: 'description',
          maxLength: 500,
          tone: 'professional'
        }),
        this.suggestTags(project)
      ])

      return {
        title: optimizations[0],
        description: optimizations[1],
        tags: optimizations[2],
        customFields: await this.generateCustomFields(project, platform)
      }
    } catch (error) {
      console.error('平台优化失败:', error)
      
      // 降级方案
      return {
        title: project.name,
        description: project.info?.introduction || project.name,
        tags: [],
        customFields: {}
      }
    }
  }

  async batchOptimize(contents: string[], context: OptimizationContext): Promise<string[]> {
    const results: string[] = []
    
    // 批量处理，但限制并发数
    const BATCH_SIZE = 3
    
    for (let i = 0; i < contents.length; i += BATCH_SIZE) {
      const batch = contents.slice(i, i + BATCH_SIZE)
      
      try {
        const batchResults = await Promise.all(
          batch.map(content => this.optimizeContent(content, context))
        )
        results.push(...batchResults)
      } catch (error) {
        console.error(`批量优化第${Math.floor(i / BATCH_SIZE) + 1}批失败:`, error)
        // 降级：使用原始内容
        results.push(...batch)
      }
    }
    
    return results
  }

  // 私有方法
  private async callOptimizeAPI(content: string, context: OptimizationContext): Promise<string> {
    if (!this.config) {
      throw new Error('AI服务配置未初始化')
    }

    const url = `${this.config.apiUrl}/optimize`

    // 构建符合API规范的请求
    const prompt = this.buildPrompt(context)
    const requestPayload: OptimizationRequest = {
      text: content,
      prompt,
      context: this.buildContext(context),
      max_length: context.maxLength,
      tone: context.tone
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    // 付费用户添加认证头
    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestPayload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`AI API请求失败 (${response.status}): ${errorText}`)
      }

      const result: OptimizationResponse = await response.json()
      
      if (!result.success) {
        throw new Error('AI优化失败')
      }

      console.log(`AI优化完成 (${result.tier} tier, confidence: ${result.confidence}%)`)
      return result.optimized_text
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('AI服务请求超时')
      }
      
      throw error
    }
  }

  private buildPrompt(context: OptimizationContext): string {
    let prompt = ''

    switch (context.fieldType) {
      case 'title':
        prompt = '请优化这个标题，使其更具吸引力和专业性'
        if (context.targetPlatform) {
          prompt += `，适合在${context.targetPlatform}平台使用`
        }
        break
      
      case 'description':
        prompt = '请优化这个描述，提高可读性和专业性'
        if (context.targetPlatform) {
          prompt += `，针对${context.targetPlatform}平台进行优化`
        }
        break
      
      case 'tags':
        prompt = '请从这个内容中提取或生成相关的标签关键词'
        break
      
      default:
        prompt = '请优化这个内容，提高其质量和可读性'
    }

    if (context.maxLength) {
      prompt += `，限制在${context.maxLength}个字符以内`
    }

    if (context.tone) {
      const toneMap = {
        professional: '专业正式',
        casual: '轻松随意',
        marketing: '营销推广',
        technical: '技术专业'
      }
      prompt += `，使用${toneMap[context.tone]}的语调`
    }

    return prompt
  }

  private buildContext(context: OptimizationContext): string {
    let contextStr = ''

    if (context.targetPlatform) {
      contextStr += `目标平台: ${context.targetPlatform}\n`
    }

    if (context.fieldType) {
      contextStr += `内容类型: ${context.fieldType}\n`
    }

    return contextStr.trim()
  }

  private buildProjectDescription(project: Project): string {
    let description = `项目名称: ${project.name}\n项目域名: ${project.domain}`

    if (project.info?.introduction) {
      description += `\n项目介绍: ${project.info.introduction}`
    }

    if (project.category) {
      description += `\n项目类型: ${project.category}`
    }

    return description
  }

  private extractKeywordsFromProject(project: Project): string[] {
    const keywords = new Set<string>()

    // 从项目名称提取
    const nameWords = project.name.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
    
    nameWords.forEach(word => keywords.add(word))

    // 从介绍提取常见技术词汇
    const techKeywords = [
      'react', 'vue', 'angular', 'node', 'python', 'java', 'javascript', 'typescript',
      'mobile', 'web', 'app', 'api', 'database', 'ai', 'ml', 'blockchain', 'iot'
    ]

    const introductionLower = (project.info?.introduction || project.name).toLowerCase()
    techKeywords.forEach(keyword => {
      if (introductionLower.includes(keyword)) {
        keywords.add(keyword)
      }
    })

    // 从分类添加关键词
    if (project.category) {
      keywords.add(project.category.toLowerCase())
    }

    return Array.from(keywords).slice(0, 8)
  }

  private async generateCustomFields(project: Project, platform: string): Promise<Record<string, string>> {
    // 根据平台生成自定义字段
    const customFields: Record<string, string> = {}

    try {
      // 这里可以根据不同平台的要求生成特定字段
      switch (platform.toLowerCase()) {
        case 'producthunt':
          customFields.hunter_comment = await this.optimizeContent(
            `为什么推荐 ${project.name}`,
            { fieldType: 'description', maxLength: 200, tone: 'marketing' }
          )
          break
        
        case 'hacker_news':
          customFields.hn_title = await this.optimizeContent(
            project.name,
            { fieldType: 'title', maxLength: 80, tone: 'casual' }
          )
          break
        
        case 'reddit':
          customFields.reddit_post = await this.optimizeContent(
            project.info?.introduction || project.name,
            { fieldType: 'description', maxLength: 300, tone: 'casual' }
          )
          break
      }
    } catch (error) {
      console.error('生成自定义字段失败:', error)
    }

    return customFields
  }

  private getBrowserFingerprint(): string {
    // 生成简单的浏览器指纹
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.textBaseline = 'top'
      ctx.font = '14px Arial'
      ctx.fillText('LinkTrackPro', 2, 2)
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // 生成简短的哈希
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }
}