import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON>ey } from "@/models/user";
import { z } from "zod";

const optimizeContentSchema = z.object({
  text: z.string().min(1, "Text is required"),
  prompt: z.string().min(1, "Prompt is required"),
  context: z.string().optional(),
  max_length: z.number().optional(),
  tone: z.enum(["professional", "casual", "marketing", "technical"]).optional()
});

// 导出一个异步函数，用于处理POST请求
export async function POST(request: NextRequest) {
  try {
    // 获取请求头中的authorization字段
    const authHeader = request.headers.get("authorization");
    // 获取请求的body
    const body = await request.json();
    
    // Validate request body first
    console.log("Validating request body, body is ", body);
    const validation = optimizeContentSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { text, prompt, context, max_length, tone } = validation.data;
    
    // Determine AI credentials to use
    let aiApiKey: string | undefined;
    let aiModelName: string;
    let aiApiBaseUrl: string;
    let userTier = "free";
    
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const apiKey = authHeader.replace("Bearer ", "");
      
      if (apiKey) {
        // Try to validate API key and get user
        const user = await getUserByApiKey(apiKey);
        
        if (user) {
          // Authenticated user - use premium AI credentials
          aiApiKey = process.env.AI_API_KEY;
          aiModelName = process.env.AI_MODEL_NAME || "gpt-3.5-turbo";
          aiApiBaseUrl = process.env.AI_API_BASE_URL || "https://api.openai.com/v1";
          userTier = "premium";
        }
      }
    }
    
    // If no authenticated user, use free AI credentials
    if (userTier === "free") {
      aiApiKey = process.env.FREE_AI_API_KEY;
      aiModelName = process.env.FREE_AI_MODEL_NAME || "gpt-3.5-turbo";
      aiApiBaseUrl = process.env.FREE_AI_API_BASE_URL || "https://api.openai.com/v1";
    }
    
    if (!aiApiKey) {
      console.error(`${userTier.toUpperCase()}_AI_API_KEY not configured`);
      
      // Fallback to basic optimization
      const fallbackOptimization = performBasicOptimization(text, prompt, max_length, tone);
      
      return NextResponse.json({
        success: true,
        ...fallbackOptimization,
        source: "fallback",
        tier: userTier
      });
    }
    
    // Construct optimization prompt
    let systemPrompt = `You are a professional content optimizer. ${prompt}`;
    
    if (tone) {
      systemPrompt += ` Use a ${tone} tone.`;
    }
    
    if (max_length) {
      systemPrompt += ` Keep the content under ${max_length} characters.`;
    }
    
    if (context) {
      systemPrompt += ` Additional context: ${context}`;
    }
    
    // Apply tier-specific limitations for free users
    let maxTokens = max_length ? Math.min(max_length * 2, 1000) : 1000;
    if (userTier === "free") {
      maxTokens = Math.min(maxTokens, 500); // Limit free users to 500 tokens
    }
    
    // Call AI API
    const aiResponse = await fetch(`${aiApiBaseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${aiApiKey}`,
      },
      body: JSON.stringify({
        model: aiModelName,
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user", 
            content: `Please optimize this text: "${text}"`
          }
        ],
        max_tokens: maxTokens,
        temperature: userTier === "free" ? 0.5 : 0.7 // Lower creativity for free tier
      })
    });
    
    if (!aiResponse.ok) {
      console.error("AI API error:", aiResponse.status, aiResponse.statusText);
      
      // Fallback to basic optimization
      const fallbackOptimization = performBasicOptimization(text, prompt, max_length, tone);
      
      return NextResponse.json({
        success: true,
        ...fallbackOptimization,
        source: "fallback",
        tier: userTier
      });
    }
    
    const aiResult = await aiResponse.json();
    
    if (aiResult.choices && aiResult.choices[0]?.message?.content) {
      const optimizedText = aiResult.choices[0].message.content.trim();
      
      // Analyze improvements
      const improvements = analyzeImprovements(text, optimizedText, prompt);
      
      return NextResponse.json({
        success: true,
        optimized_text: optimizedText,
        original_length: text.length,
        optimized_length: optimizedText.length,
        improvements,
        confidence: calculateConfidence(text, optimizedText, improvements),
        source: "ai",
        tier: userTier
      });
    } else {
      // Fallback to basic optimization
      const fallbackOptimization = performBasicOptimization(text, prompt, max_length, tone);
      
      return NextResponse.json({
        success: true,
        ...fallbackOptimization,
        source: "fallback",
        tier: userTier
      });
    }
  } catch (error) {
    console.error("Extension optimize content error:", error);
    
    // Fallback to basic optimization on error
    try {
      const bodyData = await request.json();
      const fallbackOptimization = performBasicOptimization(
        bodyData.text, 
        bodyData.prompt, 
        bodyData.max_length, 
        bodyData.tone
      );
      
      return NextResponse.json({
        success: true,
        ...fallbackOptimization,
        source: "fallback",
        tier: "free"
      });
    } catch {
      return NextResponse.json(
        { success: false, message: "Internal server error" },
        { status: 500 }
      );
    }
  }
}

// Fallback content optimization when AI service is unavailable
function performBasicOptimization(
  text: string, 
  prompt: string, 
  maxLength?: number, 
  tone?: string
): {
  optimized_text: string;
  original_length: number;
  optimized_length: number;
  improvements: string[];
  confidence: number;
} {
  let optimizedText = text;
  const improvements: string[] = [];
  
  // Basic text improvements
  
  // Remove extra whitespace
  optimizedText = optimizedText.replace(/\s+/g, ' ').trim();
  if (optimizedText !== text) {
    improvements.push("Normalized whitespace");
  }
  
  // Capitalize first letter
  if (optimizedText.length > 0 && optimizedText[0] !== optimizedText[0].toUpperCase()) {
    optimizedText = optimizedText[0].toUpperCase() + optimizedText.slice(1);
    improvements.push("Capitalized first letter");
  }
  
  // Ensure proper punctuation
  if (optimizedText.length > 0 && !optimizedText.match(/[.!?]$/)) {
    optimizedText += '.';
    improvements.push("Added proper punctuation");
  }
  
  // Basic tone adjustments
  if (tone === "professional") {
    // Replace casual words with professional alternatives
    const replacements = [
      { from: /\bawesome\b/gi, to: "excellent" },
      { from: /\bgreat\b/gi, to: "outstanding" },
      { from: /\bokay\b/gi, to: "acceptable" },
      { from: /\bstuff\b/gi, to: "content" },
      { from: /\bguys\b/gi, to: "team" }
    ];
    
    let hasReplacements = false;
    replacements.forEach(replacement => {
      const newText = optimizedText.replace(replacement.from, replacement.to);
      if (newText !== optimizedText) {
        optimizedText = newText;
        hasReplacements = true;
      }
    });
    
    if (hasReplacements) {
      improvements.push("Enhanced professional tone");
    }
  }
  
  // Length optimization
  if (maxLength && optimizedText.length > maxLength) {
    // Simple truncation with ellipsis
    optimizedText = optimizedText.substring(0, maxLength - 3) + "...";
    improvements.push(`Shortened to ${maxLength} characters`);
  }
  
  // SEO-related improvements for certain prompts
  if (prompt.toLowerCase().includes("seo")) {
    // Basic SEO improvements
    if (!optimizedText.toLowerCase().includes("quality") && optimizedText.length < 200) {
      optimizedText = optimizedText.replace(/\.$/, " with quality content.");
      improvements.push("Added SEO-friendly keywords");
    }
  }
  
  return {
    optimized_text: optimizedText,
    original_length: text.length,
    optimized_length: optimizedText.length,
    improvements,
    confidence: calculateBasicConfidence(improvements.length, text.length, optimizedText.length)
  };
}

function analyzeImprovements(original: string, optimized: string, prompt: string): string[] {
  const improvements = [];
  
  // Length comparison
  if (optimized.length !== original.length) {
    if (optimized.length < original.length) {
      improvements.push("Reduced text length for clarity");
    } else {
      improvements.push("Expanded content for better detail");
    }
  }
  
  // Grammar and style improvements (basic heuristics)
  const originalSentences = original.split(/[.!?]+/).length;
  const optimizedSentences = optimized.split(/[.!?]+/).length;
  
  if (optimizedSentences !== originalSentences) {
    improvements.push("Improved sentence structure");
  }
  
  // Word choice improvements
  const originalWords = original.toLowerCase().split(/\s+/);
  const optimizedWords = optimized.toLowerCase().split(/\s+/);
  
  const wordsChanged = originalWords.filter(word => !optimizedWords.includes(word)).length;
  if (wordsChanged > 0) {
    improvements.push("Enhanced vocabulary and word choice");
  }
  
  // Prompt-specific improvements
  if (prompt.toLowerCase().includes("professional")) {
    improvements.push("Applied professional tone");
  }
  
  if (prompt.toLowerCase().includes("seo")) {
    improvements.push("Optimized for search engines");
  }
  
  if (prompt.toLowerCase().includes("clarity")) {
    improvements.push("Improved clarity and readability");
  }
  
  return improvements.length > 0 ? improvements : ["General content optimization"];
}

function calculateConfidence(original: string, optimized: string, improvements: string[]): number {
  let confidence = 60; // Base confidence for AI optimization
  
  // Boost confidence based on improvements
  confidence += improvements.length * 10;
  
  // Boost if significant changes were made
  const changeRatio = Math.abs(optimized.length - original.length) / original.length;
  if (changeRatio > 0.1) {
    confidence += 15;
  }
  
  // Cap at 95
  return Math.min(confidence, 95);
}

function calculateBasicConfidence(improvementsCount: number, originalLength: number, optimizedLength: number): number {
  let confidence = 40; // Base confidence for fallback
  
  // Add points for each improvement
  confidence += improvementsCount * 8;
  
  // Add points for length optimization
  if (optimizedLength !== originalLength) {
    confidence += 10;
  }
  
  // Cap at 70 for fallback optimization
  return Math.min(confidence, 70);
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}